{"__meta": {"id": "01K2QGBS0GWAMJD8RVEK8BCMV4", "datetime": "2025-08-15 18:37:35", "utime": **********.633143, "method": "GET", "uri": "/admin/blog/posts/widgets/recent-posts", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 51, "start": 1755283052.135908, "end": **********.633169, "duration": 3.4972610473632812, "duration_str": "3.5s", "measures": [{"label": "Booting", "start": 1755283052.135908, "relative_start": 0, "end": **********.80632, "relative_end": **********.80632, "duration": 1.****************, "duration_str": "1.67s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.806342, "relative_start": 1.****************, "end": **********.633172, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.83s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.836542, "relative_start": 1.****************, "end": **********.850175, "relative_end": **********.850175, "duration": 0.013633012771606445, "duration_str": "13.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/blog::widgets.posts", "start": **********.904912, "relative_start": 1.****************, "end": **********.904912, "relative_end": **********.904912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.cell", "start": **********.526407, "relative_start": 3.****************, "end": **********.526407, "relative_end": **********.526407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.cell", "start": **********.527863, "relative_start": 3.3919551372528076, "end": **********.527863, "relative_end": **********.527863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.cell", "start": **********.528273, "relative_start": 3.3923652172088623, "end": **********.528273, "relative_end": **********.528273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.index", "start": **********.528634, "relative_start": 3.392726182937622, "end": **********.528634, "relative_end": **********.528634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.529579, "relative_start": 3.3936710357666016, "end": **********.529579, "relative_end": **********.529579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.536428, "relative_start": 3.400520086288452, "end": **********.536428, "relative_end": **********.536428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.537117, "relative_start": 3.4012091159820557, "end": **********.537117, "relative_end": **********.537117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.537483, "relative_start": 3.4015750885009766, "end": **********.537483, "relative_end": **********.537483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.537938, "relative_start": 3.4020302295684814, "end": **********.537938, "relative_end": **********.537938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.545583, "relative_start": 3.409675121307373, "end": **********.545583, "relative_end": **********.545583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.546173, "relative_start": 3.4102652072906494, "end": **********.546173, "relative_end": **********.546173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.546492, "relative_start": 3.4105842113494873, "end": **********.546492, "relative_end": **********.546492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.546865, "relative_start": 3.410957098007202, "end": **********.546865, "relative_end": **********.546865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.558102, "relative_start": 3.422194004058838, "end": **********.558102, "relative_end": **********.558102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.559041, "relative_start": 3.423133134841919, "end": **********.559041, "relative_end": **********.559041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.559365, "relative_start": 3.423457145690918, "end": **********.559365, "relative_end": **********.559365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.559729, "relative_start": 3.423821210861206, "end": **********.559729, "relative_end": **********.559729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.56699, "relative_start": 3.431082010269165, "end": **********.56699, "relative_end": **********.56699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.567619, "relative_start": 3.431711196899414, "end": **********.567619, "relative_end": **********.567619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.567885, "relative_start": 3.4319770336151123, "end": **********.567885, "relative_end": **********.567885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.568264, "relative_start": 3.432356119155884, "end": **********.568264, "relative_end": **********.568264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.577129, "relative_start": 3.441220998764038, "end": **********.577129, "relative_end": **********.577129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.577979, "relative_start": 3.4420711994171143, "end": **********.577979, "relative_end": **********.577979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.578383, "relative_start": 3.4424750804901123, "end": **********.578383, "relative_end": **********.578383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.579002, "relative_start": 3.44309401512146, "end": **********.579002, "relative_end": **********.579002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.587939, "relative_start": 3.452031135559082, "end": **********.587939, "relative_end": **********.587939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.588649, "relative_start": 3.4527411460876465, "end": **********.588649, "relative_end": **********.588649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.589103, "relative_start": 3.453195095062256, "end": **********.589103, "relative_end": **********.589103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.590216, "relative_start": 3.454308032989502, "end": **********.590216, "relative_end": **********.590216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.597158, "relative_start": 3.461250066757202, "end": **********.597158, "relative_end": **********.597158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.597867, "relative_start": 3.46195912361145, "end": **********.597867, "relative_end": **********.597867, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.598165, "relative_start": 3.462257146835327, "end": **********.598165, "relative_end": **********.598165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.598486, "relative_start": 3.462578058242798, "end": **********.598486, "relative_end": **********.598486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.604148, "relative_start": 3.4682400226593018, "end": **********.604148, "relative_end": **********.604148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.604764, "relative_start": 3.4688560962677, "end": **********.604764, "relative_end": **********.604764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.607225, "relative_start": 3.4713170528411865, "end": **********.607225, "relative_end": **********.607225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.609006, "relative_start": 3.473098039627075, "end": **********.609006, "relative_end": **********.609006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.6168, "relative_start": 3.4808921813964844, "end": **********.6168, "relative_end": **********.6168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.617391, "relative_start": 3.481483221054077, "end": **********.617391, "relative_end": **********.617391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.617635, "relative_start": 3.481727123260498, "end": **********.617635, "relative_end": **********.617635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.617949, "relative_start": 3.482041120529175, "end": **********.617949, "relative_end": **********.617949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.624848, "relative_start": 3.4889400005340576, "end": **********.624848, "relative_end": **********.624848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.625601, "relative_start": 3.4896931648254395, "end": **********.625601, "relative_end": **********.625601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.625874, "relative_start": 3.4899661540985107, "end": **********.625874, "relative_end": **********.625874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.index", "start": **********.626284, "relative_start": 3.4903759956359863, "end": **********.626284, "relative_end": **********.626284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.index", "start": **********.626772, "relative_start": 3.4908640384674072, "end": **********.626772, "relative_end": **********.626772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.627567, "relative_start": 3.491659164428711, "end": **********.630083, "relative_end": **********.630083, "duration": 0.002516031265258789, "duration_str": "2.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52010448, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 47, "nb_templates": 47, "templates": [{"name": "plugins/blog::widgets.posts", "param_count": null, "params": [], "start": **********.904879, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/blog/resources/views/widgets/posts.blade.phpplugins/blog::widgets.posts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fblog%2Fresources%2Fviews%2Fwidgets%2Fposts.blade.php&line=1", "ajax": false, "filename": "posts.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.cell", "param_count": null, "params": [], "start": **********.526364, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/header/cell.blade.php8def1252668913628243c4d363bee1ef::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.cell", "param_count": null, "params": [], "start": **********.527822, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/header/cell.blade.php8def1252668913628243c4d363bee1ef::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.cell", "param_count": null, "params": [], "start": **********.52824, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/header/cell.blade.php8def1252668913628243c4d363bee1ef::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.index", "param_count": null, "params": [], "start": **********.5286, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/header/index.blade.php8def1252668913628243c4d363bee1ef::table.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.529544, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.536389, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.537084, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.537449, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.537905, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.545556, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.546153, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.546464, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.546837, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.558057, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.559002, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.559329, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.559694, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.566951, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.567586, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.567852, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.568232, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.577079, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.577942, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.578359, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.578952, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.5879, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.588591, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.58907, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.590183, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.597131, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.597831, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.598145, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.598451, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.604124, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.604726, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.607078, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.60895, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.616764, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.617359, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.617607, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.617921, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.624781, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.625567, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.625842, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.index", "param_count": null, "params": [], "start": **********.626252, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/body/index.blade.php8def1252668913628243c4d363bee1ef::table.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.index", "param_count": null, "params": [], "start": **********.62674, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/core/base/resources/views/components/table/index.blade.php8def1252668913628243c4d363bee1ef::table.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00279, "accumulated_duration_str": "2.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.870744, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 17.563}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.8768, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "martfury", "explain": null, "start_percent": 17.563, "width_percent": 16.129}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.8831842, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 33.692, "width_percent": 16.846}, {"sql": "select * from `posts` order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/blog/src/Http/Controllers/PostController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\blog\\src\\Http\\Controllers\\PostController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.891814, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 50.538, "width_percent": 29.749}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (2, 3, 4, 5, 6, 7, 8, 9, 10, 11) and `slugs`.`reference_type` = 'Botble\\\\Blog\\\\Models\\\\Post'", "type": "query", "params": [], "bindings": ["Botble\\Blog\\Models\\Post"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/blog/src/Http/Controllers/PostController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\blog\\src\\Http\\Controllers\\PostController.php", "line": 105}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.898524, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 80.287, "width_percent": 19.713}]}, "models": {"data": {"Botble\\Blog\\Models\\Post": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 22, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 22}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://martfury.gc/admin/blog/posts/widgets/recent-posts", "action_name": "posts.widget.recent-posts", "controller_action": "Botble\\Blog\\Http\\Controllers\\PostController@getWidgetRecentPosts", "uri": "GET admin/blog/posts/widgets/recent-posts", "permission": "posts.index", "controller": "Botble\\Blog\\Http\\Controllers\\PostController@getWidgetRecentPosts<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FHttp%2FControllers%2FPostController.php&line=97\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Blog\\Http\\Controllers", "prefix": "admin/blog/posts", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FHttp%2FControllers%2FPostController.php&line=97\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/blog/src/Http/Controllers/PostController.php:97-110</a>", "middleware": "web, core, auth", "duration": "3.48s", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1157383439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1157383439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-945782952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-945782952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1166648796 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlRTMktzZHVUYVZEYkRYVnN0UmI1WWc9PSIsInZhbHVlIjoiSVAyZlozRGhCTjlDWFFJbThkL0g3cWVyRm9nekViSGRrWHo4N0JkbnYyOGZWKzB1NnZ2cmh0cTA0YzRXelFiMWNKUmpQNHZ6ZnFQanc4VDZjUTBWWUtvNUtXZ3crNzFCUkkwSm12RlFQUW5uZ3BhTmoweXlhdmp6UG9XeHVYbGwiLCJtYWMiOiI2MTUwYWVhMWE1ZDcxZjYxZmIwMzQ4MjZiM2RlMGRiZjA5ZmQ2MDNlYzJiMGI0OWYwNjY5ODBlYWM1MmUyNWE5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://martfury.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3328 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; xxx111otrckid=4e1e47de-b6ce-42c8-a48d-367a97cd2968; ajs_anonymous_id=%22d13d80ea-7fe8-40be-a19b-193267ad504f%22; XSRF-TOKEN=eyJpdiI6IlRTMktzZHVUYVZEYkRYVnN0UmI1WWc9PSIsInZhbHVlIjoiSVAyZlozRGhCTjlDWFFJbThkL0g3cWVyRm9nekViSGRrWHo4N0JkbnYyOGZWKzB1NnZ2cmh0cTA0YzRXelFiMWNKUmpQNHZ6ZnFQanc4VDZjUTBWWUtvNUtXZ3crNzFCUkkwSm12RlFQUW5uZ3BhTmoweXlhdmp6UG9XeHVYbGwiLCJtYWMiOiI2MTUwYWVhMWE1ZDcxZjYxZmIwMzQ4MjZiM2RlMGRiZjA5ZmQ2MDNlYzJiMGI0OWYwNjY5ODBlYWM1MmUyNWE5IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkcwbnQvN2puUnpuWVYxRzE2OFRDM3c9PSIsInZhbHVlIjoiV2RQbEZiY05ZMVA2RytGOVRLcEFJRUpMWTZvNkdXVURWV1dDV0VsOXZ1Q1BYczNQVlFTcG5GeE5aa2JBT2JYUjMycnNRZkw1NE10eHBkdUs0bHdQOG9lelo1WU9tb2J3K2REV1NvZkRXVmY2eUhLSGdKSmVOQlY2cXZFZ1N5Y1YiLCJtYWMiOiI5MDY2NzQ1MTQyYWIwYjQ1NmIzYjM4ZDczNWY2MzA2OWQzNTJlYjcxZGNmYjA5NWUwMmRmZTc4MGE3MTFjYzYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166648796\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1584830534 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>xxx111otrckid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzcOpLY5BY4Qmb2ysxF4UMkeFxNzIwhg5XNVcWQ7</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rOB2sRYY2tEGNk5tY11YcouH4tG83UStulFD0FGr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584830534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-99104456 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 18:37:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99104456\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1527145056 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzcOpLY5BY4Qmb2ysxF4UMkeFxNzIwhg5XNVcWQ7</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://martfury.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755282575\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755282575</span></span> {<a class=sf-dump-ref>#4252</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000109c0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:07:59.789559 from now\nDST Off\">2025-08-15 18:29:35.855897 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527145056\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://martfury.gc/admin/blog/posts/widgets/recent-posts", "action_name": "posts.widget.recent-posts", "controller_action": "Botble\\Blog\\Http\\Controllers\\PostController@getWidgetRecentPosts"}, "badge": null}}